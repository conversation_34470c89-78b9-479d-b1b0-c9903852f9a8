<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Achievement;
use App\Models\User;
use App\Services\AchievementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * Admin Achievement Controller
 * 
 * This controller handles admin operations for achievements including:
 * - Manually awarding achievements to users
 * - Removing achievements from users
 * - Triggering achievement checks
 * - Managing achievement data
 */
class AchievementController extends Controller
{
    protected AchievementService $achievementService;

    public function __construct(AchievementService $achievementService)
    {
        $this->achievementService = $achievementService;
    }

    /**
     * Get all achievements
     */
    public function index(Request $request): JsonResponse
    {
        $query = Achievement::query();

        // Filter by system/custom
        if ($request->has('type')) {
            if ($request->type === 'system') {
                $query->system();
            } elseif ($request->type === 'custom') {
                $query->custom();
            }
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        $achievements = $query->orderBy('name')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $achievements
        ]);
    }

    /**
     * Get achievement statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = $this->achievementService->getAchievementStatistics();
        $popularAchievements = $this->achievementService->getMostPopularAchievements();

        return response()->json([
            'success' => true,
            'data' => [
                'statistics' => $stats,
                'popular_achievements' => $popularAchievements
            ]
        ]);
    }

    /**
     * Get user's achievements
     */
    public function userAchievements(User $user): JsonResponse
    {
        $achievements = $this->achievementService->getUserAchievements($user);
        $availableAchievements = $this->achievementService->getAvailableAchievementsForUser($user);

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'achievements' => $achievements,
                'available_achievements' => $availableAchievements
            ]
        ]);
    }

    /**
     * Manually award achievement to user
     */
    public function awardAchievement(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'achievement_slug' => 'required|exists:achievements,slug',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::findOrFail($request->user_id);
        $admin = auth()->user();

        $result = $this->achievementService->manuallyAwardAchievement(
            $user,
            $request->achievement_slug,
            $admin
        );

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Remove achievement from user
     */
    public function removeAchievement(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'achievement_slug' => 'required|exists:achievements,slug',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::findOrFail($request->user_id);
        $admin = auth()->user();

        $result = $this->achievementService->removeAchievementFromUser(
            $user,
            $request->achievement_slug,
            $admin
        );

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Trigger achievement check for specific user
     */
    public function triggerUserCheck(User $user): JsonResponse
    {
        $this->achievementService->triggerAchievementCheckForUser($user);

        return response()->json([
            'success' => true,
            'message' => 'Achievement check triggered for user'
        ]);
    }

    /**
     * Trigger achievement check for all users
     */
    public function triggerAllUsersCheck(): JsonResponse
    {
        $this->achievementService->triggerAchievementCheckForAllUsers();

        return response()->json([
            'success' => true,
            'message' => 'Achievement check triggered for all users'
        ]);
    }

    /**
     * Create custom achievement
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $achievement = Achievement::create([
            'name' => $request->name,
            'description' => $request->description,
            'icon' => $request->icon ?? 'ti-trophy',
            'is_system' => false,
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Achievement created successfully',
            'data' => $achievement
        ], 201);
    }

    /**
     * Update custom achievement
     */
    public function update(Request $request, Achievement $achievement): JsonResponse
    {
        // Prevent updating system achievements
        if ($achievement->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'System achievements cannot be modified'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $achievement->update([
            'name' => $request->name,
            'description' => $request->description,
            'icon' => $request->icon ?? $achievement->icon,
            'updated_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Achievement updated successfully',
            'data' => $achievement
        ]);
    }

    /**
     * Delete custom achievement
     */
    public function destroy(Achievement $achievement): JsonResponse
    {
        // Prevent deleting system achievements
        if ($achievement->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'System achievements cannot be deleted'
            ], 403);
        }

        $achievement->update(['deleted_by' => auth()->id()]);
        $achievement->delete();

        return response()->json([
            'success' => true,
            'message' => 'Achievement deleted successfully'
        ]);
    }
}
