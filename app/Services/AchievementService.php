<?php

namespace App\Services;

use App\Models\Achievement;
use App\Models\User;
use App\Jobs\ProcessAchievementQueueJob;
use App\Notifications\AchievementAwardedNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Achievement Service
 * 
 * This service handles achievement management operations including:
 * - Manual achievement awarding by admins
 * - Triggering achievement checks for users
 * - Managing achievement data
 */
class AchievementService
{
    protected AchievementConditionsService $conditionsService;

    public function __construct(AchievementConditionsService $conditionsService)
    {
        $this->conditionsService = $conditionsService;
    }

    /**
     * Manually award an achievement to a user (admin function)
     */
    public function manuallyAwardAchievement(User $user, string $achievementSlug, User $admin): array
    {
        try {
            $achievement = Achievement::where('slug', $achievementSlug)->first();
            
            if (!$achievement) {
                return [
                    'success' => false,
                    'message' => 'Achievement not found'
                ];
            }

            // Check if user already has this achievement
            $hasAchievement = $user->achievements()
                ->where('achievement_id', $achievement->id)
                ->exists();

            if ($hasAchievement) {
                return [
                    'success' => false,
                    'message' => 'User already has this achievement'
                ];
            }

            // Award the achievement
            $user->achievements()->attach($achievement->id, [
                'achieved_at' => now(),
            ]);

            // Send notification
            $user->notify(new AchievementAwardedNotification($achievementSlug));

            Log::info('Achievement manually awarded', [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'achievement_id' => $achievement->id,
                'awarded_by' => $admin->id
            ]);

            return [
                'success' => true,
                'message' => 'Achievement awarded successfully',
                'achievement' => $achievement
            ];

        } catch (\Exception $e) {
            Log::error('Failed to manually award achievement', [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'admin_id' => $admin->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to award achievement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Remove an achievement from a user (admin function)
     */
    public function removeAchievementFromUser(User $user, string $achievementSlug, User $admin): array
    {
        try {
            $achievement = Achievement::where('slug', $achievementSlug)->first();
            
            if (!$achievement) {
                return [
                    'success' => false,
                    'message' => 'Achievement not found'
                ];
            }

            // Check if user has this achievement
            $hasAchievement = $user->achievements()
                ->where('achievement_id', $achievement->id)
                ->exists();

            if (!$hasAchievement) {
                return [
                    'success' => false,
                    'message' => 'User does not have this achievement'
                ];
            }

            // Remove the achievement
            $user->achievements()->detach($achievement->id);

            Log::info('Achievement removed from user', [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'achievement_id' => $achievement->id,
                'removed_by' => $admin->id
            ]);

            return [
                'success' => true,
                'message' => 'Achievement removed successfully',
                'achievement' => $achievement
            ];

        } catch (\Exception $e) {
            Log::error('Failed to remove achievement from user', [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'admin_id' => $admin->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to remove achievement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Trigger achievement check for a specific user
     */
    public function triggerAchievementCheckForUser(User $user): void
    {
        ProcessAchievementQueueJob::dispatchForUser($user);
        
        Log::info('Achievement check triggered for user', [
            'user_id' => $user->id
        ]);
    }

    /**
     * Trigger achievement check for all users
     */
    public function triggerAchievementCheckForAllUsers(): void
    {
        ProcessAchievementQueueJob::dispatchForAllUsers();
        
        Log::info('Achievement check triggered for all users');
    }

    /**
     * Get user's achievements with details
     */
    public function getUserAchievements(User $user): array
    {
        return $user->achievements()
            ->withPivot('achieved_at')
            ->orderByDesc('user_achievements.achieved_at')
            ->get()
            ->map(function ($achievement) {
                return [
                    'id' => $achievement->id,
                    'name' => $achievement->name,
                    'slug' => $achievement->slug,
                    'description' => $achievement->description,
                    'icon' => $achievement->icon,
                    'is_system' => $achievement->is_system,
                    'achieved_at' => $achievement->pivot->achieved_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get available achievements that user doesn't have
     */
    public function getAvailableAchievementsForUser(User $user): array
    {
        $userAchievementIds = $user->achievements()->pluck('achievement_id')->toArray();
        
        return Achievement::whereNotIn('id', $userAchievementIds)
            ->orderBy('name')
            ->get()
            ->map(function ($achievement) {
                return [
                    'id' => $achievement->id,
                    'name' => $achievement->name,
                    'slug' => $achievement->slug,
                    'description' => $achievement->description,
                    'icon' => $achievement->icon,
                    'is_system' => $achievement->is_system,
                ];
            })
            ->toArray();
    }

    /**
     * Get achievement statistics
     */
    public function getAchievementStatistics(): array
    {
        $totalAchievements = Achievement::count();
        $systemAchievements = Achievement::system()->count();
        $customAchievements = Achievement::custom()->count();
        
        $totalAwarded = DB::table('user_achievements')->count();
        $uniqueUsersWithAchievements = DB::table('user_achievements')
            ->distinct('user_id')
            ->count();

        return [
            'total_achievements' => $totalAchievements,
            'system_achievements' => $systemAchievements,
            'custom_achievements' => $customAchievements,
            'total_awarded' => $totalAwarded,
            'unique_users_with_achievements' => $uniqueUsersWithAchievements,
        ];
    }

    /**
     * Get most popular achievements
     */
    public function getMostPopularAchievements(int $limit = 10): array
    {
        return Achievement::select('achievements.*')
            ->join('user_achievements', 'achievements.id', '=', 'user_achievements.achievement_id')
            ->selectRaw('achievements.*, COUNT(user_achievements.id) as awarded_count')
            ->groupBy('achievements.id')
            ->orderByDesc('awarded_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
