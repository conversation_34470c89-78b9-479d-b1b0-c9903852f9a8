<div class="table-responsive px-5">
    <div class="col-md-12 mt-4 mb-4 d-flex justify-content-between align-items-center">
        <div class="d-flex gap-2 flex-grow-1">
            <input type="text" wire:model.live.debounce.399ms="search" class="form-control"
                placeholder="{{ $searchPlaceholder }}" style="max-width: 300px;" />

            <!-- Verification Filter (only show for UsersTable) -->
            @if(property_exists($this, 'verificationFilter'))
                <select wire:model.live="verificationFilter" class="form-select" style="max-width: 200px;">
                    <option value="all">جميع المستخدمين</option>
                    <option value="verified">موثق</option>
                    <option value="unverified">غير موثق</option>
                    <option value="pending">قيد المراجعة</option>
                </select>
            @endif

            <!-- Achievement Filter (only show for UsersTable) -->
            @if(property_exists($this, 'achievementFilter'))
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="min-width: 200px;">
                        @if($achievementFilter)
                            {{ \App\Models\Achievement::find($achievementFilter)?->name ?? 'فلتر الإنجازات' }}
                        @else
                            فلتر الإنجازات
                        @endif
                    </button>
                    <ul class="dropdown-menu" style="max-height: 300px; overflow-y: auto;">
                        <li><a class="dropdown-item" href="#" wire:click="$set('achievementFilter', '')">جميع المستخدمين</a></li>
                        <li><hr class="dropdown-divider"></li>
                        @foreach(\App\Models\Achievement::orderBy('name')->get() as $achievement)
                            <li>
                                <a class="dropdown-item {{ $achievementFilter == $achievement->id ? 'active' : '' }}"
                                   href="#"
                                   wire:click="$set('achievementFilter', {{ $achievement->id }})">
                                    <i class="ti ti-{{ $achievement->icon ?? 'trophy' }} me-2"></i>
                                    {{ $achievement->name }}
                                    <span class="badge bg-primary ms-2">{{ $achievement->users_count }}</span>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Status Filter (for verification requests table) -->
            @if(property_exists($this, 'statusFilter'))
                <select wire:model.live="statusFilter" class="form-select" style="max-width: 200px;">
                    <option value="all">جميع الطلبات</option>
                    <option value="pending">قيد المراجعة</option>
                    <option value="approved">مقبول</option>
                    <option value="rejected">مرفوض</option>
                </select>
            @endif
        </div>

        <div class="dropdown ms-2">
            <button class="btn btn-secondary" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="ti ti-filter"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                @foreach ($columns as $column)
                    <li>
                        <a class="dropdown-item" href="javascript:void(0)"
                            wire:click="toggleColumnVisibility('{{ $column['field'] }}')">
                            <input type="checkbox" @if (in_array($column['field'], $visibleColumns)) checked @endif>
                            {{ $column['title'] }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <!-- Removed the original dropdown code from here -->
    </div>
    <table class="table">
        <thead class="border-top">
            <tr>
                @foreach ($columns as $column)
                    @if (in_array($column['field'], $visibleColumns))
                        <x-datatable.table-header field="{{ $column['field'] }}" title="{{ $column['title'] }}"
                            customClass="{{ $column['class'] ?? '' }}" :sortBy="$sortField" :sortDir="$sortDirection"
                            :sortable="$column['sortable'] ?? true" />
                    @endif
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $item)
                <tr>
                    @foreach ($columns as $column)
                        @if (in_array($column['field'], $visibleColumns) && $column['field'] != 'actions')
                            <td>{!! $this->renderColumn($column, $item) !!}</td>
                        @endif
                    @endforeach
                    <td>
                        <div class="d-flex align-items-center">
                            @foreach ($this->actions as $actionType)
                                {!! $this->renderAction($actionType, $item) !!}
                            @endforeach
                        </div>
                    </td>
                </tr>
                @if (isset($updateModalView))
                    @include($updateModalView)
                @endif
            @endforeach
        </tbody>
    </table>
    @include('components.table.delete-modal')

    <div class="col-12 p-3">
        {{ $data->links() }}
    </div>

    <!-- JavaScript functions for the component -->
    <script>
        function openDeleteModal(item) {
            $('#deleteModal').modal('show');
            if (item.deleteMessage != undefined) {
                $('#deleteModalMessage').text(item.deleteMessage);
            }
            $('#deleteForm').attr('action', item.delete);
        }

        // Verification management functions
        function verifyUser(userId) {
            Swal.fire({
                title: 'توثيق المستخدم',
                text: 'هل أنت متأكد من توثيق هذا المستخدم؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، وثق',
                cancelButtonText: 'إلغاء',
                input: 'textarea',
                inputPlaceholder: 'ملاحظات إضافية (اختياري)',
                customClass: {
                    confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/verifications/verify-user/${userId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            admin_notes: result.value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'تم!',
                                text: data.message,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                            Livewire.dispatch('refreshTable');
                        } else {
                            Swal.fire({
                                title: 'خطأ!',
                                text: data.message || 'حدث خطأ أثناء التوثيق',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-danger waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'خطأ!',
                            text: 'حدث خطأ في الاتصال',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-danger waves-effect waves-light'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        }

        function unverifyUser(userId) {
            Swal.fire({
                title: 'إلغاء توثيق المستخدم',
                text: 'هل أنت متأكد من إلغاء توثيق هذا المستخدم؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، ألغ التوثيق',
                cancelButtonText: 'إلغاء',
                customClass: {
                    confirmButton: 'btn btn-warning me-3 waves-effect waves-light',
                    cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/admin/verifications/unverify-user/${userId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'تم!',
                                text: data.message || 'تم إلغاء توثيق المستخدم',
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-success waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                            Livewire.dispatch('refreshTable');
                        } else {
                            Swal.fire({
                                title: 'خطأ!',
                                text: data.message || 'حدث خطأ أثناء إلغاء التوثيق',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-danger waves-effect waves-light'
                                },
                                buttonsStyling: false
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'خطأ!',
                            text: 'حدث خطأ في الاتصال',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-danger waves-effect waves-light'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        }

        function openAddAchievementModal(userId) {
            // Get available achievements for the user
            fetch(`/admin/users/${userId}/available-achievements`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.achievements.length > 0) {
                        // Create options for achievements
                        let options = {};
                        data.achievements.forEach(achievement => {
                            options[achievement.slug] = achievement.name;
                        });

                        Swal.fire({
                            title: 'منح إنجاز للمستخدم',
                            html: `
                                <div class="mb-3">
                                    <label for="achievement-select" class="form-label">اختر الإنجاز</label>
                                    <select id="achievement-select" class="form-select">
                                        <option value="">اختر إنجاز</option>
                                        ${Object.entries(options).map(([value, text]) => `<option value="${value}">${text}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="achievement-year" class="form-label">السنة</label>
                                    <select id="achievement-year" class="form-select">
                                        ${(() => {
                                            const currentYear = new Date().getFullYear();
                                            let yearOptions = '';
                                            for (let year = currentYear; year >= currentYear - 5; year--) {
                                                yearOptions += `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`;
                                            }
                                            return yearOptions;
                                        })()}
                                    </select>
                                </div>
                            `,
                            showCancelButton: true,
                            confirmButtonText: 'منح الإنجاز',
                            cancelButtonText: 'إلغاء',
                            customClass: {
                                confirmButton: 'btn btn-primary me-3 waves-effect waves-light',
                                cancelButton: 'btn btn-label-secondary waves-effect waves-light'
                            },
                            buttonsStyling: false,
                            preConfirm: () => {
                                const achievement = document.getElementById('achievement-select').value;
                                const year = document.getElementById('achievement-year').value;

                                if (!achievement) {
                                    Swal.showValidationMessage('يجب اختيار إنجاز!');
                                    return false;
                                }
                                if (!year) {
                                    Swal.showValidationMessage('يجب اختيار السنة!');
                                    return false;
                                }

                                return { achievement, year };
                            }
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Award the achievement
                                fetch('/admin/achievements/award', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                    },
                                    body: JSON.stringify({
                                        user_id: userId,
                                        achievement_slug: result.value.achievement,
                                        achievement_year: result.value.year
                                    })
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        Swal.fire({
                                            title: 'تم!',
                                            text: data.message,
                                            icon: 'success',
                                            customClass: {
                                                confirmButton: 'btn btn-success waves-effect waves-light'
                                            },
                                            buttonsStyling: false
                                        });
                                        Livewire.dispatch('refreshTable');
                                    } else {
                                        Swal.fire({
                                            title: 'خطأ!',
                                            text: data.message,
                                            icon: 'error',
                                            customClass: {
                                                confirmButton: 'btn btn-danger waves-effect waves-light'
                                            },
                                            buttonsStyling: false
                                        });
                                    }
                                })
                                .catch(error => {
                                    Swal.fire({
                                        title: 'خطأ!',
                                        text: 'حدث خطأ في الاتصال',
                                        icon: 'error',
                                        customClass: {
                                            confirmButton: 'btn btn-danger waves-effect waves-light'
                                        },
                                        buttonsStyling: false
                                    });
                                });
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'تنبيه',
                            text: 'لا توجد إنجازات متاحة لهذا المستخدم',
                            icon: 'info',
                            customClass: {
                                confirmButton: 'btn btn-info waves-effect waves-light'
                            },
                            buttonsStyling: false
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: 'خطأ!',
                        text: 'حدث خطأ في تحميل الإنجازات',
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-danger waves-effect waves-light'
                        },
                        buttonsStyling: false
                    });
                });
        }
    </script>
</div>
